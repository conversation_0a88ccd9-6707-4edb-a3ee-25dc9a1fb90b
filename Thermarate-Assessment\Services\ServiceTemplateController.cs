using AutoMapper;
using AutoMapper.QueryableExtensions;
using Kendo.DynamicLinq;
using RediSoftware.BusinessLogic;
using RediSoftware.Common;
using RediSoftware.Helpers;
using RediSoftware.Dtos;
using RediSoftware.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Results;
using Ganss.Excel;
using RediSoftware.App_Start;
using ScratchTools;
using NPOI.SS.Formula.Functions;
using System.Net.Http;

namespace RediSoftware.Services
{

    [Authorize]
    [RoutePrefix("api/ServiceTemplate")]
    public class ServiceTemplateController : ApiController
    {
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private ServiceTemplate _serviceContext;

        public ServiceTemplateController(IUnitOfWork unitOfWork,
                                      IMapper mapper,
                                      ServiceTemplate constructionFactory)
        {
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            _serviceContext = constructionFactory;
        }

        ///// <summary>
        ///// Return a single record based on its Id
        ///// </summary>
        public IHttpActionResult Get([FromUri] Guid serviceTemplateId)
        {
            return Ok(_serviceContext.Get(serviceTemplateId));
        }

        /// <summary>
        /// Return a list
        /// </summary>
        public IHttpActionResult Get(string fromDate = null, string toDate = null, bool? isDeleted = false, [FromUri] PagingParameters paging = null)
        {
            var s = _serviceContext.GetList(fromDate, toDate, isDeleted, paging);
            return Ok(s);
        }


        public IHttpActionResult GetAll()
        {
            var s = _serviceContext.GetAll();
            return Ok(s);
        }

        [HttpPost]
        public IHttpActionResult Create([FromBody] ServiceTemplateDto serviceDto)
        {
            var guid = _serviceContext.Create(serviceDto);
            return Ok(guid);
        }

        [HttpPost]
        public IHttpActionResult Update([FromBody] ServiceTemplateDto serviceDto)
        {
            _serviceContext.Update(serviceDto);
            return Ok();
        }

        [HttpPost]
        public IHttpActionResult Delete([FromUri] Guid serviceTemplateId)
        {
            _serviceContext.Delete(serviceTemplateId);
            return Ok();
        }

        [HttpPost]
        public IHttpActionResult UndoDelete([FromUri] Guid serviceTemplateId)
        {
            _serviceContext.UndoDelete(serviceTemplateId);
            return Ok();
        }

        [HttpPost]
        public IHttpActionResult SetIsFavourite([FromUri] Guid serviceTemplateId, bool isFavourite)
        {
            _serviceContext.SetIsFavourite(serviceTemplateId, isFavourite);
            return Ok(_serviceContext.Get(serviceTemplateId));
        }

        [HttpPost]
        public IHttpActionResult Copy([FromUri] Guid serviceTemplateId)
        {
            var id = _serviceContext.Copy(serviceTemplateId);
            return Ok(id);
        }

        [HttpPost]
        public async Task<IHttpActionResult> GetMultiFiltered([FromBody] FilterDataDto filterData)
        {
            var result = _serviceContext.GetListMultiFiltered(filterData);
            return Ok(result);
        }

        [HttpPost]
        public async Task<IHttpActionResult> GetMultiFilterOptions([FromBody] List<MultiFilterFieldDto> fieldsList)
        {
            var filterOptions = _serviceContext.GetMultiFilterOptions(fieldsList);
            return Ok(filterOptions);
        }

        [HttpPost]
        public async Task<IHttpActionResult> GetFilterCountData([FromBody] FilterDataDto filterData)
        {
            var filterCountData = _serviceContext.GetFilterCountData(filterData);
            return Ok(filterCountData);
        }

        [HttpGet]
        public IHttpActionResult GetServiceTypes()
        {
            var list = _unitOfWork.Context.RSS_ServiceType
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<ServiceTypeDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetServiceCategories()
        {
            var list = _unitOfWork.Context.RSS_ServiceCategory
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<ServiceCategoryDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetHeatingSystemTypes()
        {
            var list = _unitOfWork.Context.RSS_HeatingSystemType
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<HeatingSystemTypeDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetICRatings()
        {
            var list = _unitOfWork.Context.RSS_ICRating
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<ICRatingDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetServiceControlDevices()
        {
            var list = _unitOfWork.Context.RSS_ServiceControlDevice
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<ServiceControlDeviceDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetServiceFuelTypes()
        {
            var list = _unitOfWork.Context.RSS_ServiceFuelType.Where(a => a.Deleted == false)
                                                              .OrderBy(a => a.SortOrder)
                                                              .ProjectTo<ServiceFuelTypeDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetServicePumpTypes()
        {
            var list = _unitOfWork.Context.RSS_ServicePumpType.Where(a => a.Deleted == false)
                                                              .OrderBy(a => a.SortOrder)
                                                              .ProjectTo<ServicePumpTypeDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        [HttpGet]
        public IHttpActionResult GetServiceBatteryTypes()
        {
            var list = _unitOfWork.Context.RSS_ServiceBatteryType.Where(a => a.Deleted == false)
                                                                 .OrderBy(a => a.SortOrder)
                                                                 .ProjectTo<ServiceBatteryTypeDto>(_mapper.ConfigurationProvider);

            return Ok(list);
        }

        /// <summary>
        /// Exports the service template database to an Excel file in the same format as expected for imports
        /// </summary>
        /// <param name="ids">Optional list of service template IDs to export. If not provided, all non-deleted templates will be exported.</param>
        /// <returns>Excel file as a response</returns>
        [HttpPost]
        public HttpResponseMessage ExportTemplateDatabase([FromBody] List<Guid> ids)
        {
            try
            {
                // Create a new Excel package
                using (var package = new OfficeOpenXml.ExcelPackage())
                {
                    // Get service templates based on provided IDs or all if no IDs provided
                    var query = _unitOfWork.Context.RSS_ServiceTemplate.Where(s => !s.Deleted);

                    // Filter by IDs if provided
                    if (ids != null && ids.Any())
                    {
                        query = query.Where(s => ids.Contains(s.ServiceTemplateId));
                    }

                    var services = query.ProjectTo<ServiceTemplateDto>(_mapper.ConfigurationProvider)
                        .ToList();

                    // Group services by category
                    var spaceHeating = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "SpaceHeatingSystem").ToList();
                    var spaceCooling = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "SpaceCoolingSystem").ToList();
                    var hotWaterSystems = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "HotWaterSystem").ToList();
                    var cooktop = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "Cooktop").ToList();
                    var oven = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "Oven").ToList();
                    var artificialLighting = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "ArtificialLighting").ToList();
                    var exhaustFans = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "ExhaustFans").ToList();
                    var ceilingVents = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "CeilingVents").ToList();
                    var ceilingFans = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "CeilingFans").ToList();
                    var photovoltaicSystems = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "PhotovoltaicSystem").ToList();
                    var swimmingPools = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "SwimmingPool").ToList();
                    var spas = services.Where(s => s.ServiceCategory?.ServiceCategoryCode == "Spa").ToList();

                    // Create worksheets for each category
                    CreateServiceWorksheet(package, "Heating System", spaceHeating);
                    CreateServiceWorksheet(package, "Cooling System", spaceCooling);
                    CreateServiceWorksheet(package, "Hot Water System", hotWaterSystems);
                    CreateServiceWorksheet(package, "Cooktop", cooktop);
                    CreateServiceWorksheet(package, "Oven", oven);
                    CreateServiceWorksheet(package, "Artificial Lighting", artificialLighting);
                    CreateServiceWorksheet(package, "Exhaust Fans", exhaustFans);
                    CreateServiceWorksheet(package, "Ceiling Vents", ceilingVents);
                    CreateServiceWorksheet(package, "Ceiling Fans", ceilingFans);
                    CreateServiceWorksheet(package, "Photovoltaic System", photovoltaicSystems);
                    CreateServiceWorksheet(package, "Swimming Pool", swimmingPools);
                    CreateServiceWorksheet(package, "Spa", spas);

                    // Return the Excel file
                    var response = new HttpResponseMessage(System.Net.HttpStatusCode.OK)
                    {
                        Content = new ByteArrayContent(package.GetAsByteArray())
                    };
                    response.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    response.Content.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
                    {
                        FileName = "ServicesDatabase.xlsx"
                    };
                    return response;
                }
            }
            catch (Exception ex)
            {
                throw new HttpResponseException(System.Net.HttpStatusCode.InternalServerError);
            }
        }

        private void CreateServiceWorksheet(OfficeOpenXml.ExcelPackage package, string name, List<ServiceTemplateDto> services)
        {
            if (services == null || !services.Any())
                return;

            var worksheet = package.Workbook.Worksheets.Add(name);

            // Helper method to get the export title for a service type
            string GetServiceTypeExportTitle(ServiceTypeDto serviceType, string categoryCode)
            {
                if (serviceType == null)
                    return null;

                var mapping = SERVICE_TYPE_TITLE_MAPPING.FirstOrDefault(m =>
                    m.Code == serviceType.ServiceTypeCode &&
                    (m.Category == categoryCode || m.GroupCategory == categoryCode));

                return mapping != null ? mapping.ExportTitle : serviceType.Title;
            }

            // Add headers based on the category
            if (name == "Heating System")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "System Type";
                worksheet.Cells[1, 9].Value = "Fuel Type";
                worksheet.Cells[1, 10].Value = "Energy Rating";
                worksheet.Cells[1, 11].Value = "Ducted";
                worksheet.Cells[1, 12].Value = "Flued";
                worksheet.Cells[1, 13].Value = "Cost ($)";
                worksheet.Cells[1, 14].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 15].Value = "Embodied Water (L)";
                worksheet.Cells[1, 16].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Cooling System")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "System Type";
                worksheet.Cells[1, 9].Value = "Fuel Type";
                worksheet.Cells[1, 10].Value = "Energy Rating";
                worksheet.Cells[1, 11].Value = "Ducted";
                worksheet.Cells[1, 12].Value = "Cost ($)";
                worksheet.Cells[1, 13].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 14].Value = "Embodied Water (L)";
                worksheet.Cells[1, 15].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Hot Water System")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "System Type";
                worksheet.Cells[1, 9].Value = "Fuel Type";
                worksheet.Cells[1, 10].Value = "Energy Rating";
                worksheet.Cells[1, 11].Value = "Cost ($)";
                worksheet.Cells[1, 12].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 13].Value = "Embodied Water (L)";
                worksheet.Cells[1, 14].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Artificial Lighting")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Lamp Type";
                worksheet.Cells[1, 9].Value = "Lamp Power Rating (W)";
                worksheet.Cells[1, 10].Value = "Control Device";
                worksheet.Cells[1, 11].Value = "Recessed";
                worksheet.Cells[1, 12].Value = "Sealed";
                worksheet.Cells[1, 13].Value = "IC Rating";
                worksheet.Cells[1, 14].Value = "Cut Out Diameter (mm)";
                worksheet.Cells[1, 15].Value = "Cost ($)";
                worksheet.Cells[1, 16].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 17].Value = "Embodied Water (L)";
                worksheet.Cells[1, 18].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Cooktop")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Cooktop Type";
                worksheet.Cells[1, 9].Value = "Fuel Type";
                worksheet.Cells[1, 10].Value = "Energy Rating";
                worksheet.Cells[1, 11].Value = "Cost ($)";
                worksheet.Cells[1, 12].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 13].Value = "Embodied Water (L)";
                worksheet.Cells[1, 14].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Oven")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Oven Type";
                worksheet.Cells[1, 9].Value = "Energy Rating";
                worksheet.Cells[1, 10].Value = "Cost ($)";
                worksheet.Cells[1, 11].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 12].Value = "Embodied Water (L)";
                worksheet.Cells[1, 13].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Exhaust Fans")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Lamp Type";
                worksheet.Cells[1, 9].Value = "Lamp Power Rating (W)";
                worksheet.Cells[1, 10].Value = "Recessed";
                worksheet.Cells[1, 11].Value = "Sealed";
                worksheet.Cells[1, 12].Value = "Cut Out Diameter (mm)";
                worksheet.Cells[1, 13].Value = "Cost ($)";
                worksheet.Cells[1, 14].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 15].Value = "Embodied Water (L)";
                worksheet.Cells[1, 16].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Ceiling Vents")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Recessed";
                worksheet.Cells[1, 9].Value = "Sealed";
                worksheet.Cells[1, 10].Value = "Length (mm)";
                worksheet.Cells[1, 11].Value = "Width (mm)";
                worksheet.Cells[1, 12].Value = "Cost ($)";
                worksheet.Cells[1, 13].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 14].Value = "Embodied Water (L)";
                worksheet.Cells[1, 15].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Ceiling Fans")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Lamp Type";
                worksheet.Cells[1, 9].Value = "Lamp Power Rating (W)";
                worksheet.Cells[1, 10].Value = "Blade Diameter (mm)";
                worksheet.Cells[1, 11].Value = "Permanently Installed";
                worksheet.Cells[1, 12].Value = "Speed Controller";
                worksheet.Cells[1, 13].Value = "Cost ($)";
                worksheet.Cells[1, 14].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 15].Value = "Embodied Water (L)";
                worksheet.Cells[1, 16].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Photovoltaic System")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Array Capacity (kW)";
                worksheet.Cells[1, 9].Value = "Area (m²)";
                worksheet.Cells[1, 10].Value = "Azimuth (°)";
                worksheet.Cells[1, 11].Value = "Pitch (°)";
                worksheet.Cells[1, 12].Value = "Shade Factor (%)";
                worksheet.Cells[1, 13].Value = "Inverter Capacity (kW)";
                worksheet.Cells[1, 14].Value = "Battery Type";
                worksheet.Cells[1, 15].Value = "Battery Capacity (kWh)";
                worksheet.Cells[1, 16].Value = "Cost ($)";
                worksheet.Cells[1, 17].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 18].Value = "Embodied Water (L)";
                worksheet.Cells[1, 19].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Swimming Pool")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Volume (L)";
                worksheet.Cells[1, 9].Value = "Fuel Type";
                worksheet.Cells[1, 10].Value = "Pump Type";
                worksheet.Cells[1, 11].Value = "Pump Energy Rating";
                worksheet.Cells[1, 12].Value = "Heating System";
                worksheet.Cells[1, 13].Value = "Cover";
                worksheet.Cells[1, 14].Value = "Time Switch";
                worksheet.Cells[1, 15].Value = "Cost ($)";
                worksheet.Cells[1, 16].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 17].Value = "Embodied Water (L)";
                worksheet.Cells[1, 18].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else if (name == "Spa")
            {
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "Volume (L)";
                worksheet.Cells[1, 9].Value = "Fuel Type";
                worksheet.Cells[1, 10].Value = "Pump Type";
                worksheet.Cells[1, 11].Value = "Pump Energy Rating";
                worksheet.Cells[1, 12].Value = "Heating System";
                worksheet.Cells[1, 13].Value = "Cover";
                worksheet.Cells[1, 14].Value = "Time Switch";
                worksheet.Cells[1, 15].Value = "Cost ($)";
                worksheet.Cells[1, 16].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 17].Value = "Embodied Water (L)";
                worksheet.Cells[1, 18].Value = "Embodied GHG Emissions (kgCO₂e)";
            }
            else
            {
                // Default headers for any other category
                worksheet.Cells[1, 1].Value = "Category";
                worksheet.Cells[1, 2].Value = "Description";
                worksheet.Cells[1, 3].Value = "Display Description";
                worksheet.Cells[1, 4].Value = "Show in Report";
                worksheet.Cells[1, 5].Value = "Manufacturer";
                worksheet.Cells[1, 6].Value = "Unit of Measurement";
                worksheet.Cells[1, 7].Value = "Favourite";
                worksheet.Cells[1, 8].Value = "System Type";
                worksheet.Cells[1, 9].Value = "Fuel Type";
                worksheet.Cells[1, 10].Value = "Energy Rating";
                worksheet.Cells[1, 11].Value = "Cost ($)";
                worksheet.Cells[1, 12].Value = "Embodied Energy (MJ)";
                worksheet.Cells[1, 13].Value = "Embodied Water (L)";
                worksheet.Cells[1, 14].Value = "Embodied GHG Emissions (kgCO₂e)";
            }

            // Add data
            for (int i = 0; i < services.Count; i++)
            {
                var service = services[i];
                int row = i + 2;

                if (name == "Heating System")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode);
                    worksheet.Cells[row, 9].Value = service.ServiceFuelType?.Title;
                    worksheet.Cells[row, 10].Value = service.StarRating2019;
                    worksheet.Cells[row, 11].Value = (service.IsDucted ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 12].Value = (service.IsFlued ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 16].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Cooling System")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode);
                    worksheet.Cells[row, 9].Value = service.ServiceFuelType?.Title;
                    worksheet.Cells[row, 10].Value = service.StarRating2019;
                    worksheet.Cells[row, 11].Value = (service.IsDucted ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 12].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Hot Water System")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode);
                    worksheet.Cells[row, 9].Value = service.ServiceFuelType?.Title;
                    worksheet.Cells[row, 10].Value = service.StarRating2019;
                    worksheet.Cells[row, 11].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 12].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Artificial Lighting")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode);
                    worksheet.Cells[row, 9].Value = service.LampPowerRating;
                    worksheet.Cells[row, 10].Value = service.ServiceControlDevice?.Title;
                    worksheet.Cells[row, 11].Value = (service.IsRecessed ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 12].Value = (service.IsSealed ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 13].Value = service.ICRating?.Title;
                    worksheet.Cells[row, 14].Value = service.CutOutDiameter;
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 16].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 17].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 18].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Cooktop")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode);
                    worksheet.Cells[row, 9].Value = service.ServiceFuelType?.Title;
                    worksheet.Cells[row, 10].Value = service.StarRating2019;
                    worksheet.Cells[row, 11].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 12].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Oven")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode);
                    worksheet.Cells[row, 9].Value = service.StarRating2019;
                    worksheet.Cells[row, 10].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 11].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 12].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Exhaust Fans")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode); // Lamp Type
                    worksheet.Cells[row, 9].Value = service.LampPowerRating; // Lamp Power Rating
                    worksheet.Cells[row, 10].Value = (service.IsRecessed ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 11].Value = (service.IsSealed ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 12].Value = service.CutOutDiameter;
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 16].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Ceiling Vents")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = (service.IsRecessed ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 9].Value = (service.IsSealed ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 10].Value = service.Length;
                    worksheet.Cells[row, 11].Value = service.Width;
                    worksheet.Cells[row, 12].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Ceiling Fans")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode); // Lamp Type
                    worksheet.Cells[row, 9].Value = service.LampPowerRating; // Lamp Power Rating
                    worksheet.Cells[row, 10].Value = service.BladeDiameter;
                    worksheet.Cells[row, 11].Value = (service.IsPermanentlyInstalled ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 12].Value = (service.HasSpeedController ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 16].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Photovoltaic System")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = service.SystemCapacity;
                    worksheet.Cells[row, 9].Value = service.Area;
                    worksheet.Cells[row, 10].Value = service.Azimuth;
                    worksheet.Cells[row, 11].Value = service.Pitch;
                    worksheet.Cells[row, 12].Value = service.ShadeFactor;
                    worksheet.Cells[row, 13].Value = service.InverterCapacity;
                    worksheet.Cells[row, 14].Value = service.ServiceBatteryType?.Title;
                    worksheet.Cells[row, 15].Value = service.BatteryCapacity;
                    worksheet.Cells[row, 16].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 17].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 18].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 19].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Swimming Pool")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = service.Volume;
                    worksheet.Cells[row, 9].Value = service.ServiceFuelType?.Title;
                    worksheet.Cells[row, 10].Value = service.ServicePumpType?.Title;
                    worksheet.Cells[row, 11].Value = service.StarRating2019;
                    worksheet.Cells[row, 12].Value = service.HeatingSystemType?.Title;
                    worksheet.Cells[row, 13].Value = (service.HasCover ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 14].Value = (service.HasTimeSwitch ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 16].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 17].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 18].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else if (name == "Spa")
                {
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = service.Volume;
                    worksheet.Cells[row, 9].Value = service.ServiceFuelType?.Title;
                    worksheet.Cells[row, 10].Value = service.ServicePumpType?.Title;
                    worksheet.Cells[row, 11].Value = service.StarRating2019;
                    worksheet.Cells[row, 12].Value = service.HeatingSystemType?.Title;
                    worksheet.Cells[row, 13].Value = (service.HasCover ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 14].Value = (service.HasTimeSwitch ?? false) ? "Yes" : "No";
                    worksheet.Cells[row, 15].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 16].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 17].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 18].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
                else
                {
                    // Default data mapping for any other category
                    worksheet.Cells[row, 1].Value = service.ServiceCategory?.Title;
                    worksheet.Cells[row, 2].Value = service.Description;
                    worksheet.Cells[row, 3].Value = service.DisplayDescription;
                    worksheet.Cells[row, 4].Value = service.ShowInReport ? "Yes" : "No";
                    worksheet.Cells[row, 5].Value = service.Manufacturer?.Description;
                    worksheet.Cells[row, 6].Value = service.UnitOfMeasure?.Title;
                    worksheet.Cells[row, 7].Value = service.IsFavourite ? "Yes" : "No";
                    worksheet.Cells[row, 8].Value = GetServiceTypeExportTitle(service.ServiceType, service.ServiceCategory?.ServiceCategoryCode);
                    worksheet.Cells[row, 9].Value = service.ServiceFuelType?.Title;
                    worksheet.Cells[row, 10].Value = service.StarRating2019;
                    worksheet.Cells[row, 11].Value = service.LifeCycleData?.CostPerM2;
                    worksheet.Cells[row, 12].Value = service.LifeCycleData?.EmbodiedEnergy;
                    worksheet.Cells[row, 13].Value = service.LifeCycleData?.EmbodiedWater;
                    worksheet.Cells[row, 14].Value = service.LifeCycleData?.EmbodiedGhgEmissions;
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            // Center align all cells
            using (var range = worksheet.Cells[1, 1, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column])
            {
                range.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                range.Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
            }

            // Make headers bold
            using (var range = worksheet.Cells[1, 1, 1, worksheet.Dimension.End.Column])
            {
                range.Style.Font.Bold = true;
            }
        }

        /// <summary>
        /// Accepts an excel file and attempts to process it. During the first process, any warnings or
        /// errors should require the user to click "ok, I get it, process anyway" - at this point
        /// query again with forceImport = true.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IHttpActionResult> UploadTemplateDatabase(bool forceImport = false)
        {
            var stream = await Request.Content.ReadAsStreamAsync();
            if (stream.Length == 0)
                return BadRequest("No content found. Did you add an excel file to the content-body and set the mime-type?");

            // Used to keep track of any warnings or errors generated during the import process.
            // Will be returned to the user so they can diagnose any problems...
            var report = new ProcessReport();

            try
            {
                // Validate Excel sheet names before processing and get list of missing sheets
                var missingSheets = await ValidateExcelSheetNames(stream, report);

                ExcelMapper serviceMapper = await CreateMapperForServiceData(stream, report);

                var sheet_spaceHeating = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Heating System", report, missingSheets);
                var sheet_spaceCooling = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Cooling System", report, missingSheets);
                var sheet_hotWaterSystems = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Hot Water System", report, missingSheets);
                var sheet_cooktop = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Cooktop", report, missingSheets);
                var sheet_oven = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Oven", report, missingSheets);
                var sheet_artificialLighting = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Artificial Lighting", report, missingSheets);
                var sheet_exhaustFans = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Exhaust Fans", report, missingSheets);
                var sheet_ceilingVents = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Ceiling Vents", report, missingSheets);
                var sheet_ceilingFans = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Ceiling Fans", report, missingSheets);
                var sheet_photovoltaicSystems = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Photovoltaic System", report, missingSheets);
                var sheet_swimmingPools = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Swimming Pool", report, missingSheets);
                var sheet_spas = FetchWithSheetNameValidation<ServiceTemplateDto>(serviceMapper, "Spa", report, missingSheets);

                var services = sheet_spaceHeating.Concat(sheet_spaceCooling)
                                                 .Concat(sheet_hotWaterSystems)
                                                 .Concat(sheet_cooktop)
                                                 .Concat(sheet_oven)
                                                 .Concat(sheet_artificialLighting)
                                                 .Concat(sheet_exhaustFans)
                                                 .Concat(sheet_ceilingVents)
                                                 .Concat(sheet_ceilingFans)
                                                 .Concat(sheet_photovoltaicSystems)
                                                 .Concat(sheet_swimmingPools)
                                                 .Concat(sheet_spas)
                                                 .ToList();

                // For the 'pre-process' we will only import the actual data to our database
                // if there are NO errors or warnings at all. Otherwise the user must confirm
                // they are happy with the warnings or errors shown.
                if ((report.Warnings.Count == 0 && report.Errors.Count == 0) || forceImport)
                {
                    if (services != null && services.Any())
                        report += BulkUpsert(services, _unitOfWork, _mapper, _unitOfWork.Context.RSS_ServiceTemplate);

                    report.Success = true;
                    return Ok(report);
                }
                else
                {
                    report.Success = false;
                    return Ok(report);
                }

            }
            catch (Exception e)
            {
                return InternalServerError(e);
            }


        }

        private async Task<ExcelMapper> CreateMapperForServiceData(Stream stream, ProcessReport warnings)
        {

            // Load all required 'enum' types.
            var categories = _unitOfWork.Context.RSS_ServiceCategory.ProjectTo<ServiceCategoryDto>(_mapper.ConfigurationProvider);
            var serviceTypes = _unitOfWork.Context.RSS_ServiceType.ProjectTo<ServiceTypeDto>(_mapper.ConfigurationProvider);
            var fuelTypes = _unitOfWork.Context.RSS_ServiceFuelType.ProjectTo<ServiceFuelTypeDto>(_mapper.ConfigurationProvider);
            var batteryTypes = _unitOfWork.Context.RSS_ServiceBatteryType.ProjectTo<ServiceBatteryTypeDto>(_mapper.ConfigurationProvider);
            var pumpTypes = _unitOfWork.Context.RSS_ServicePumpType.ProjectTo<ServicePumpTypeDto>(_mapper.ConfigurationProvider);
            var controlDevices = _unitOfWork.Context.RSS_ServiceControlDevice.ProjectTo<ServiceControlDeviceDto>(_mapper.ConfigurationProvider);
            var heatingSystems = _unitOfWork.Context.RSS_HeatingSystemType.ProjectTo<HeatingSystemTypeDto>(_mapper.ConfigurationProvider);
            var icRatings = _unitOfWork.Context.RSS_ICRating.ProjectTo<ICRatingDto>(_mapper.ConfigurationProvider);
            var manufacturers = _unitOfWork.Context.RSS_Manufacturer.ProjectTo<ManufacturerDto>(_mapper.ConfigurationProvider);
            var units = _unitOfWork.Context.RSS_UnitOfMeasure.ProjectTo<UnitOfMeasureDto>(_mapper.ConfigurationProvider);

            ExcelMapper mapper = new ExcelMapper(stream);

            var now = DateTime.Now;
            var user = UtilityFunctions.CurrentUserName ?? "unknown";

            // Custom Mappings for general 'Surface' constructs.
            mapper.AddBeforeMapping<ServiceTemplateDto>((p, i) =>
            {
                p.ServiceTemplateId = Guid.NewGuid();
                p.CreatedOn = now;
                p.CreatedByName = user;
                p.Deleted = false;
                p.ShowInReport = true;
            });

            mapper.AddMapping<ServiceTemplateDto>("Manufacturer", p => p.Manufacturer)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = manufacturers.FirstOrDefault(x => x.Description == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Manufacturer '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("Unit of Measurement", p => p.UnitOfMeasure)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = units.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Unit of Measure '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("Category", p => p.ServiceCategory)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = categories.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Category '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            // Map lifecycle data fields
            mapper.AddMapping<ConstructionLifeCycleAndCost>("Cost ($)", p => p.CostPerM2)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Cost value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied Energy (MJ)", p => p.EmbodiedEnergy)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied Energy value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied Water (L)", p => p.EmbodiedWater)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied Water value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ConstructionLifeCycleAndCost>("Embodied GHG Emissions (kgCO₂e)", p => p.EmbodiedGhgEmissions)
                .SetPropertyUsing((v, cell) =>
                {
                    if (v == null || string.IsNullOrWhiteSpace(v.ToString()))
                        return null;

                    decimal result;
                    if (decimal.TryParse(v.ToString(), out result))
                        return result;

                    string warning = $"WARNING: Invalid Embodied GHG Emissions value '{v}' - must be a decimal number. Cell: {cell.Address}";
                    warnings.Warnings.Add(warning);
                    return null;
                });

            mapper.AddMapping<ServiceTemplateDto>("Fuel Type", p => p.ServiceFuelType)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = fuelTypes.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Fuel Type '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            // These map to the same thing, just have different names in some sheets.
            mapper.AddMapping<ServiceTemplateDto>("System Type", p => p.ServiceType)
                .SetPropertyUsing((v, cell) =>
                {
                    string vString = (string)v;
                    var nameMapping = SERVICE_TYPE_TITLE_MAPPING.FirstOrDefault(m => m.Title == vString || m.WohLookupTitle == vString || m.ExportTitle == vString);
                    var data = nameMapping != null
                                ? serviceTypes.FirstOrDefault(x => x.ServiceTypeCode == nameMapping.Code && (x.ForServiceCategory == nameMapping.Category || x.ForServiceCategory == nameMapping.GroupCategory))
                                : serviceTypes.FirstOrDefault(x => x.Title == vString);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Type '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<ServiceTemplateDto>("Lamp Type", p => p.ServiceType)
                .SetPropertyUsing((v, cell) =>
                {
                    string vString = (string)v;
                    var nameMapping = SERVICE_TYPE_TITLE_MAPPING.FirstOrDefault(m => m.Title == vString || m.WohLookupTitle == vString || m.ExportTitle == vString);
                    var data = nameMapping != null
                                ? serviceTypes.FirstOrDefault(x => x.ServiceTypeCode == nameMapping.Code && (x.ForServiceCategory == nameMapping.Category || x.ForServiceCategory == nameMapping.GroupCategory))
                                : serviceTypes.FirstOrDefault(x => x.Title == vString);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Lamp Type '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<ServiceTemplateDto>("Cooktop Type", p => p.ServiceType)
                .SetPropertyUsing((v, cell) =>
                {
                    string vString = (string)v;
                    var nameMapping = SERVICE_TYPE_TITLE_MAPPING.FirstOrDefault(m => m.Title == vString || m.WohLookupTitle == vString || m.ExportTitle == vString);
                    var data = nameMapping != null
                                ? serviceTypes.FirstOrDefault(x => x.ServiceTypeCode == nameMapping.Code && (x.ForServiceCategory == nameMapping.Category || x.ForServiceCategory == nameMapping.GroupCategory))
                                : serviceTypes.FirstOrDefault(x => x.Title == vString && x.ForServiceCategory == "Cooktop");
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Cooktop Type '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });
            mapper.AddMapping<ServiceTemplateDto>("Oven Type", p => p.ServiceType)
                .SetPropertyUsing((v, cell) =>
                {
                    string vString = (string)v;
                    var nameMapping = SERVICE_TYPE_TITLE_MAPPING.FirstOrDefault(m => m.Title == vString || m.WohLookupTitle == vString || m.ExportTitle == vString);
                    var data = nameMapping != null
                                ? serviceTypes.FirstOrDefault(x => x.ServiceTypeCode == nameMapping.Code && (x.ForServiceCategory == nameMapping.Category || x.ForServiceCategory == nameMapping.GroupCategory))
                                : serviceTypes.FirstOrDefault(x => x.Title == vString && x.ForServiceCategory == "Oven");
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Oven Type '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("Battery Type", p => p.ServiceBatteryType)
                .SetPropertyUsing((v, cell) =>
                {
                    string vString = (string)v;
                    var data = batteryTypes.FirstOrDefault(x => x.Title == vString);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Type '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("Control Device", p => p.ServiceControlDevice)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = controlDevices.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Control Device '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("IC Rating", p => p.ICRating)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = icRatings.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - IC Rating '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("Heating System", p => p.HeatingSystemType)
                .SetPropertyUsing((v, cell) =>
                {
                    var data = heatingSystems.FirstOrDefault(x => x.Title == (string)v);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Heating System '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("Pump Type", p => p.ServicePumpType)
                .SetPropertyUsing((v, cell) =>
                {
                    string vString = (string)v;
                    var data = pumpTypes.FirstOrDefault(x => x.Title == vString);
                    if (data == null)
                    {
                        string warning = $"WARNING: Unknown Value - Pump Type '{v}' does not exist. Cell: {cell.Address}";
                        warnings.Warnings.Add(warning);
                        return null;
                    }
                    else
                        return data;
                });

            mapper.AddMapping<ServiceTemplateDto>("Energy Rating", p => p.StarRating2019);
            mapper.AddMapping<ServiceTemplateDto>("Pump Energy Rating", p => p.StarRating2019);

            // Add explicit mappings for Photovoltaic System fields to handle both Unicode and non-Unicode versions
            mapper.AddMapping<ServiceTemplateDto>("Array Capacity (kW)", p => p.SystemCapacity);

            // Area field - handle both "Area (m²)" and "Area (m2)"
            mapper.AddMapping<ServiceTemplateDto>("Area (m²)", p => p.Area);
            mapper.AddMapping<ServiceTemplateDto>("Area (m2)", p => p.Area);

            // Azimuth field - handle both "Azimuth (°)" and "Azimuth (o)"
            mapper.AddMapping<ServiceTemplateDto>("Azimuth (°)", p => p.Azimuth);
            mapper.AddMapping<ServiceTemplateDto>("Azimuth (o)", p => p.Azimuth);

            // Pitch field - handle both "Pitch (°)" and "Pitch (o)"
            mapper.AddMapping<ServiceTemplateDto>("Pitch (°)", p => p.Pitch);
            mapper.AddMapping<ServiceTemplateDto>("Pitch (o)", p => p.Pitch);

            mapper.AddMapping<ServiceTemplateDto>("Shade Factor (%)", p => p.ShadeFactor);
            mapper.AddMapping<ServiceTemplateDto>("Inverter Capacity (kW)", p => p.InverterCapacity);
            mapper.AddMapping<ServiceTemplateDto>("Battery Capacity (kWh)", p => p.BatteryCapacity);

            // Set all bool values.
            mapper.AddMapping<ServiceTemplateDto>("Ducted", p => p.IsDucted)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<ServiceTemplateDto>("Recessed", p => p.IsRecessed)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<ServiceTemplateDto>("Flued", p => p.IsFlued)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<ServiceTemplateDto>("Sealed", p => p.IsSealed)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<ServiceTemplateDto>("PermanentlyInstalled", p => p.IsPermanentlyInstalled)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<ServiceTemplateDto>("Speed Controller", p => p.HasSpeedController)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<ServiceTemplateDto>("Cover", p => p.HasCover)
                .SetPropertyUsing(v => (string)v == "Yes");

            mapper.AddMapping<ServiceTemplateDto>("Time Switch", p => p.HasTimeSwitch)
                .SetPropertyUsing(v => (string)v == "Yes");

            // Map Favourite field
            mapper.AddMapping<ServiceTemplateDto>("Favourite", p => p.IsFavourite)
                .SetPropertyUsing(v => (string)v == "Yes");

            // After everything is mapped we set some values that are inferred via other now-mapped values.
            mapper.AddAfterMapping<ServiceTemplateDto>((p, i) =>
            {
                var lifecycleData = p.LifeCycleData;
                bool lifecycleHasData = lifecycleData.CostPerM2 != null || lifecycleData.EmbodiedEnergy != null ||
                           lifecycleData.EmbodiedWater != null || lifecycleData.EmbodiedGhgEmissions != null;

                p.LifeCycleData.HasConstructionLifeCycleAndCost = lifecycleHasData;

                // Not exactly sure why (something to do with these values being set by the mapper via reflection...?) the
                // underlying JSON parts weren't updating, and thus saving to the DB wrong. This self-reassignment fixes that.
                p.LifeCycleData = p.LifeCycleData;

                if(p.ServiceCategory != null)
                    p.HiddenTableColumns = ServiceTemplate.AssignDefaultColumnVisibility(p.ServiceCategory.ServiceCategoryCode);
            });

            // The mapper now has all custom logic associated with it as needed to map construction data.
            return mapper;

        }

        /// <summary>
        /// Attempts to update or insert all of the required construction/opening templates.
        /// </summary>
        /// <returns>A BulkUpsertReport indicating the overall success/failure of the operation.</returns>
       private static ProcessReport BulkUpsert(
            IEnumerable<ServiceTemplateDto> templates,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            System.Data.Entity.DbSet<RSS_ServiceTemplate> context)
        {
            // Start inserting data into DB. Any errors are still recorded.
            var insertionErrors = new List<string>();
            int updated = 0;
            int inserted = 0;
            int failed = 0;

            foreach (var service in templates)
            {
                var existing = context.FirstOrDefault(x => x.Description == service.Description && x.ServiceCategoryCode == service.ServiceCategory.ServiceCategoryCode && !x.Deleted);

                RSS_ServiceTemplate mapped = null;

                if (existing != null)
                {
                    var backup = existing.ServiceTemplateId;
                    service.ServiceTemplateId = existing.ServiceTemplateId;
                    mapper.Map(service, existing); // Will this work without the configuration provider..?
                    existing.ServiceTemplateId = backup;
                }
                else
                {
                    mapped = mapper.Map<RSS_ServiceTemplate>(service);
                    context.Add(mapped);
                }

                try
                {
                    unitOfWork.Context.SaveChanges();

                    if (existing != null)
                        updated++;
                    else
                        inserted++;
                }
                catch (Exception e)
                {
                    failed++;
                    insertionErrors.Add($"ERROR: Failed to insert template with ID '{service.ExternalConstructionId}' into the database. Details: {e.InnerException?.InnerException?.Message}");

                    // Detach these so they won't continue to error out on subsequent calls to saveChanges...
                    if (existing != null)
                        unitOfWork.Context.Entry(existing).State = System.Data.Entity.EntityState.Detached;
                    if(mapped != null)
                        unitOfWork.Context.Entry(mapped).State = System.Data.Entity.EntityState.Detached;
                }

            }

            return new ProcessReport() { Errors = insertionErrors, Updated = updated, Inserted = inserted, Failed = failed };
        }

        /// <summary>
        /// Validates that all expected Excel sheet names are present, handling both hyphenated and space-separated formats
        /// </summary>
        private async Task<HashSet<string>> ValidateExcelSheetNames(Stream stream, ProcessReport report)
        {
            // Create a copy of the stream for validation
            MemoryStream backup = new MemoryStream();
            stream.Position = 0;
            await stream.CopyToAsync(backup);
            backup.Position = 0;

            var missingSheets = new HashSet<string>();

            using (var package = new OfficeOpenXml.ExcelPackage(backup))
            {
                var actualSheetNames = package.Workbook.Worksheets.Select(ws => ws.Name).ToList();
                var expectedSheetNames = GetExpectedSheetNames();

                foreach (var expectedName in expectedSheetNames)
                {
                    if (!IsSheetNamePresent(actualSheetNames, expectedName))
                    {
                        missingSheets.Add(expectedName);
                    }
                }

                // Add warnings for missing sheets
                foreach (var missingSheet in missingSheets)
                {
                    report.Warnings.Add($"WARNING: Expected sheet '{missingSheet}' not found.");
                }

                // Add available sheets information only once at the bottom if there are missing sheets
                if (missingSheets.Any())
                {
                    report.Warnings.Add($"Available sheets: {string.Join(", ", actualSheetNames)}");
                }
            }

            // Reset stream position for subsequent use
            stream.Position = 0;
            return missingSheets;
        }

        /// <summary>
        /// Gets the expected sheet names for Services import
        /// </summary>
        private List<string> GetExpectedSheetNames()
        {
            return new List<string>
            {
                "Heating System",
                "Cooling System",
                "Hot Water System",
                "Cooktop",
                "Oven",
                "Artificial Lighting",
                "Exhaust Fans",
                "Ceiling Vents",
                "Ceiling Fans",
                "Photovoltaic System",
                "Swimming Pool",
                "Spa"
            };
        }

        /// <summary>
        /// Checks if a sheet name is present, handling both hyphenated and space-separated formats
        /// </summary>
        private bool IsSheetNamePresent(List<string> actualSheetNames, string expectedName)
        {
            // Check exact match first
            if (actualSheetNames.Contains(expectedName))
                return true;

            // Check with hyphens replaced by spaces
            var expectedWithSpaces = expectedName.Replace("-", " ");
            if (actualSheetNames.Contains(expectedWithSpaces))
                return true;

            // Check with spaces replaced by hyphens
            var expectedWithHyphens = expectedName.Replace(" ", "-");
            if (actualSheetNames.Contains(expectedWithHyphens))
                return true;

            return false;
        }

        /// <summary>
        /// Fetches data from a sheet with enhanced name validation, supporting both hyphenated and space-separated formats
        /// </summary>
        private List<T> FetchWithSheetNameValidation<T>(ExcelMapper mapper, string sheetName, ProcessReport report, HashSet<string> missingSheets)
        {
            // If we already know this sheet is missing, don't try to fetch and don't add another warning
            if (missingSheets.Contains(sheetName))
            {
                return new List<T>();
            }

            // Always start by replacing hyphens with spaces as the primary approach
            string normalizedSheetName = sheetName.Replace("-", " ");

            try
            {
                // Try with hyphens replaced by spaces first (primary approach)
                return mapper.Fetch<T>(normalizedSheetName).ToList();
            }
            catch (Exception ex1)
            {
                try
                {
                    // Try original name as fallback
                    return mapper.Fetch<T>(sheetName).ToList();
                }
                catch (Exception ex2)
                {
                    try
                    {
                        // Try with spaces replaced by hyphens as final fallback
                        return mapper.Fetch<T>(sheetName.Replace(" ", "-")).ToList();
                    }
                    catch (Exception ex3)
                    {
                        // Only add this warning if we haven't already warned about the sheet being missing
                        report.Warnings.Add($"WARNING: Could not fetch data from sheet '{sheetName}'. Error: {ex3.Message}");
                        return new List<T>();
                    }
                }
            }
        }

        private class ServiceTypeTitleMappingDto
        {
            public string Code;
            public string Category;
            public string GroupCategory;
            public string Title;
            public string WohLookupTitle;
            public string ExportTitle;
        }

        private List<ServiceTypeTitleMappingDto> SERVICE_TYPE_TITLE_MAPPING = new List<ServiceTypeTitleMappingDto> {
            new ServiceTypeTitleMappingDto { Code = "ElectricResistanceDucted", Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Electric Resistance (Ducted)",         WohLookupTitle = "Electric - Resistance (panel)", ExportTitle = "Panel electric resistance" },
            new ServiceTypeTitleMappingDto { Code = "ElectricResistancePanel",  Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Electric Resistance Panel",            WohLookupTitle = "Electric - Resistance (panel)", ExportTitle = "Panel electric resistance" },
            new ServiceTypeTitleMappingDto { Code = "ElectricResistanceSlab",   Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Electric Resistance Underfloor",       WohLookupTitle = "Electric - Resistance (slab)",  ExportTitle = "Slab electric resistance" },
            new ServiceTypeTitleMappingDto { Code = "GasDucted",                Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Gas (Ducted)",                         WohLookupTitle = "Gas (ducted)",                  ExportTitle = "Ducted gas" },
            new ServiceTypeTitleMappingDto { Code = "GasNonDucted",             Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Gas (Room)",                           WohLookupTitle = "Gas (non-ducted)",              ExportTitle = "Non-ducted gas" },
            new ServiceTypeTitleMappingDto { Code = "HydronicGas",              Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Hydronic Panel",                       WohLookupTitle = "Hydronic Gas",                  ExportTitle = "Ducted gas" },
            new ServiceTypeTitleMappingDto { Code = "HydronicUnderfloor",       Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Hydronic Underfloor",                  WohLookupTitle = "Hydronic Gas",                  ExportTitle = "Ducted gas" },
            new ServiceTypeTitleMappingDto { Code = "HeatPumpDucted",           Category = "SpaceHeatingSystem", GroupCategory = "SpaceSystem", Title = "Reverse-Cycle (Ducted)",               WohLookupTitle = "Heat Pump (ducted)",            ExportTitle = "Ducted heat pump" },
            new ServiceTypeTitleMappingDto { Code = "HeatPumpNonDucted",        Category = "SpaceHeatingSystem", GroupCategory = "SpaceSystem", Title = "Reverse-Cycle (Room)",                 WohLookupTitle = "Heat Pump (non-ducted)",        ExportTitle = "Non-ducted heat pump" },
            new ServiceTypeTitleMappingDto { Code = "WoodHeater",               Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "Wood",                                 WohLookupTitle = "Wood Heater",                   ExportTitle = "Wood heater" },
            new ServiceTypeTitleMappingDto { Code = "OtherOrNoneSpecified",     Category = "SpaceHeatingSystem", GroupCategory = null,          Title = "None/Unknown (Default)",               WohLookupTitle = "Other or None Specified",       ExportTitle = "Other" },

            new ServiceTypeTitleMappingDto { Code = "Evaporative",              Category = "SpaceCoolingSystem", GroupCategory = null,          Title = "Evaporative (Ducted)",                 WohLookupTitle = "Evaporative",                   ExportTitle = "Evaporative" },
            new ServiceTypeTitleMappingDto { Code = "EvaporativeRoom",          Category = "SpaceCoolingSystem", GroupCategory = null,          Title = "Evaporative (Room)",                   WohLookupTitle = "Evaporative",                   ExportTitle = "Evaporative" },
            new ServiceTypeTitleMappingDto { Code = "HeatPumpDucted",           Category = "SpaceCoolingSystem", GroupCategory = "SpaceSystem", Title = "Refrigerative (Ducted)",               WohLookupTitle = "Heat Pump (ducted)",            ExportTitle = "Ducted heat pump" },
            new ServiceTypeTitleMappingDto { Code = "HeatPumpNonDucted",        Category = "SpaceCoolingSystem", GroupCategory = "SpaceSystem", Title = "Refrigerative (Room)",                 WohLookupTitle = "Heat Pump (non-ducted)",        ExportTitle = "Non-ducted heat pump" },
            new ServiceTypeTitleMappingDto { Code = "OtherOrNoneSpecified",     Category = "SpaceCoolingSystem", GroupCategory = null,          Title = "None/Unknown (Default)",               WohLookupTitle = "Other or None Specified",       ExportTitle = "Other" },

            new ServiceTypeTitleMappingDto { Code = "ElectricInstantaneous",    Category = "HotWaterSystem",     GroupCategory = null,          Title = "Electric Instantaneous",               WohLookupTitle = "Other or None Specified",       ExportTitle = "Gas storage" },
            new ServiceTypeTitleMappingDto { Code = "ElectricStorageOffPeak",   Category = "HotWaterSystem",     GroupCategory = null,          Title = "Electric Storage (Off-Peak)",          WohLookupTitle = "Electric Storage (off peak)",   ExportTitle = "Electric storage (off peak)" },
            new ServiceTypeTitleMappingDto { Code = "ElectricStorageStandard",  Category = "HotWaterSystem",     GroupCategory = null,          Title = "Electric Storage (Peak)",              WohLookupTitle = "Electric Storage (standard)",   ExportTitle = "Electric storage (standard)" },
            new ServiceTypeTitleMappingDto { Code = "GasInstantaneous",         Category = "HotWaterSystem",     GroupCategory = null,          Title = "Gas Instantaneous",                    WohLookupTitle = "Gas Instantaneous",             ExportTitle = "Gas instantaneous" },
            new ServiceTypeTitleMappingDto { Code = "GasStorage",               Category = "HotWaterSystem",     GroupCategory = null,          Title = "Gas Storage",                          WohLookupTitle = "Gas Storage",                   ExportTitle = "Gas storage" },
            new ServiceTypeTitleMappingDto { Code = "HeatPumpStandard",         Category = "HotWaterSystem",     GroupCategory = null,          Title = "Heat Pump (Peak)",                     WohLookupTitle = "Heat Pump (standard)",          ExportTitle = "Heat pump (standard)" },
            new ServiceTypeTitleMappingDto { Code = "HeatPumpOffPeak",          Category = "HotWaterSystem",     GroupCategory = null,          Title = "Heat Pump (Off-Peak)",                 WohLookupTitle = "Heat Pump (off peak)",          ExportTitle = "Heat pump (off peak)" },
            new ServiceTypeTitleMappingDto { Code = "SolarElectricStandard",    Category = "HotWaterSystem",     GroupCategory = null,          Title = "Solar with Electric Boost (Peak)",     WohLookupTitle = "Solar Electric (standard)",     ExportTitle = "Solar electric" },
            new ServiceTypeTitleMappingDto { Code = "SolarElectricBoost",       Category = "HotWaterSystem",     GroupCategory = null,          Title = "Solar with Electric Boost (Off-Peak)", WohLookupTitle = "Solar Electric (standard)",     ExportTitle = "Solar electric" },
            new ServiceTypeTitleMappingDto { Code = "SolarGas",                 Category = "HotWaterSystem",     GroupCategory = null,          Title = "Solar with Gas Boost",                 WohLookupTitle = "Solar Gas",                     ExportTitle = "Solar gas" },
            new ServiceTypeTitleMappingDto { Code = "Wood",                     Category = "HotWaterSystem",     GroupCategory = null,          Title = "Wood",                                 WohLookupTitle = "Other or None Specified",       ExportTitle = "Gas storage" },
            new ServiceTypeTitleMappingDto { Code = "OtherOrNoneSpecified",     Category = "HotWaterSystem",     GroupCategory = null,          Title = "None/Unknown (Default)",               WohLookupTitle = "Other or None Specified",       ExportTitle = "Gas storage" }
        };

    }
}