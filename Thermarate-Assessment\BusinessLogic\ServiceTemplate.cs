using RediSoftware.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using RediSoftware.Dtos;
using AutoMapper;
using RediSoftware.Redi_Utility;
using RediSoftware.Helpers;
using RediSoftware.App_Start;
using RediSoftware.Common;
using System.Globalization;
using AutoMapper.QueryableExtensions;
using Kendo.DynamicLinq;

namespace RediSoftware.BusinessLogic
{
    /// <summary>
    /// Provides methods to update both SURFACES and OPENINGS.
    /// </summary>
    public class ServiceTemplate : BusinessLogicBase
    {
        public ServiceTemplate(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _mapper = mapper;
            _unitOfWork = unitOfWork;
        }

        public ServiceTemplateDto Get(Guid ServiceTemplateId)
        {
            _unitOfWork.ReadOnly();

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(ServiceTemplateId);
            var dto = _mapper.Map<ServiceTemplateDto>(dbModel);
            return dto;
        }

        /// <summary>
        /// Returns a summarized view of our Service Templates.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public DataSourceResult<RSS_ServiceTemplateView> GetList(
            string fromDate = null,
            string toDate = null,
            bool? isDeleted = false,
            PagingParameters paging = null)
        {
            DateTime dtFromDate = DateTime.MinValue;
            DateTime dtToDate = DateTime.MaxValue;
            if (fromDate != null)
                dtFromDate = DateTime.Parse(fromDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();
            if (toDate != null)
                dtToDate = DateTime.Parse(toDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();

            paging.SetDefaultSort("Description");
            _unitOfWork.ReadOnly();

            var templates = _unitOfWork.Context.RSS_ServiceTemplateView
                    .Where(a => (a.CreatedOn >= dtFromDate && a.CreatedOn <= dtToDate) && (a.Deleted == isDeleted))
                    .ToDataSourceResult(paging.PageSize, paging.Skip, paging.Sort, paging.Filter, paging.Aggregate, paging.Export);

            return templates;
        }

        public List<ServiceTemplateDto> GetAll()
        {
            return _unitOfWork.Context.RSS_ServiceTemplate
                .Where(x => x.Deleted == false)
                .OrderBy(x => x.Description)
                .ProjectTo<ServiceTemplateDto>(_mapper.ConfigurationProvider)
                .ToList();
        }

        public Guid Create(ServiceTemplateDto serviceTemplateDto)
        {
            serviceTemplateDto.CreatedOn = DateTime.Now;
            serviceTemplateDto.CreatedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            serviceTemplateDto.ServiceTemplateId = Guid.NewGuid();
            RSS_ServiceTemplate _dbModel = _mapper.Map<RSS_ServiceTemplate>(serviceTemplateDto);

            _unitOfWork.Context.RSS_ServiceTemplate.Add(_dbModel);
            _unitOfWork.Commit();

            this.RecordAdminEvent(AdminChangeType.Add, serviceTemplateDto.Description, "Added new ServiceTemplate.");
            _unitOfWork.Commit();
            return _dbModel.ServiceTemplateId;
        }

        public Guid Update(ServiceTemplateDto serviceTemplateDto)
        {
            serviceTemplateDto.ModifiedOn = DateTime.Now;
            serviceTemplateDto.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(serviceTemplateDto.ServiceTemplateId);

            _mapper.Map(serviceTemplateDto, dbModel);
            _unitOfWork.Commit();

            this.RecordAdminEvent(AdminChangeType.Update, serviceTemplateDto.Description, "Updated ServiceTemplate.");
            return dbModel.ServiceTemplateId;
        }

        public void Delete(Guid id)
        {
            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(id);
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            dbModel.Deleted = true;
            this.RecordAdminEvent(AdminChangeType.Delete, dbModel.Description, "");
            _unitOfWork.Commit();
        }

        public void UndoDelete(Guid id)
        {

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(id);
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            dbModel.Deleted = false;
            this.RecordAdminEvent(AdminChangeType.Delete, dbModel.Description, "");
            _unitOfWork.Commit();

        }

        public Guid Copy(Guid id)
        {
            var model = Get(id);
            model.ServiceTemplateId = Guid.NewGuid();
            model.CreatedOn = DateTime.Now;
            model.CreatedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            model.ModifiedOn = null;
            model.ModifiedByName = null;
            model.Deleted = false;
            model.Description = model.Description + " Copy";

            var mapped = _mapper.Map<RSS_ServiceTemplate>(model);
            _unitOfWork.Context.RSS_ServiceTemplate.Add(mapped);

            this.RecordAdminEvent(AdminChangeType.Delete, model.Description, "");
            _unitOfWork.Commit();

            return model.ServiceTemplateId;
        }


        public List<ServiceCategoryDto> GetServiceCategories()
        {
            var list = _unitOfWork.Context.RSS_ServiceCategory
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<ServiceCategoryDto>(_mapper.ConfigurationProvider)
                .ToList();

            return list;
        }


        public static Dictionary<string, bool> AssignDefaultColumnVisibility(string serviceCategoryCode)
        {
            // Used for intial column show/hide state.
            // Properties not in this list will be shown.

            var defaultHiddenColumns = new Dictionary<string, bool>();

            // Currently no properties are hidden by default for services. Leaving here as a stub.
            // Refer to Construction.AssignDefaultColumnVisibility for an example.

            return defaultHiddenColumns;
        }

        public void SetIsFavourite(Guid serviceTemplateId, bool isFavourite)
        {
            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(serviceTemplateId);

            if (dbModel == null)
            {
                throw new Exception(string.Format("{1} row not found for Id: {0}", serviceTemplateId, "RSS_ServiceTemplate"));
            }

            // Update the database model
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            dbModel.IsFavourite = isFavourite;

            _unitOfWork.Commit();
        }

        public DataSourceResult<RSS_ServiceTemplateView> GetListMultiFiltered(FilterDataDto filterData)
        {
            _unitOfWork.ReadOnly();

            var query = _unitOfWork.Context.RSS_ServiceTemplateView
                .Where(a => a.Deleted == false);

            // Apply multi-filters
            query = ApplyMultiFilters(query, filterData.fields, filterData.appliedFilters);

            // Apply search filters
            if (filterData.paging?.Filter != null && filterData.paging.Filter.Any())
            {
                query = ApplySearchFilters(query, filterData.paging.Filter);
            }

            var result = query.ToDataSourceResult(
                filterData.paging?.PageSize ?? 100,
                filterData.paging?.Skip ?? 0,
                filterData.paging?.Sort,
                null, // Don't apply additional filters here as we've already applied them
                filterData.paging?.Aggregate,
                filterData.paging?.Export
            );

            return result;
        }

        public Dictionary<string, List<FilterOption>> GetMultiFilterOptions(List<MultiFilterFieldDto> fieldsList)
        {
            _unitOfWork.ReadOnly();

            var filterOptions = new Dictionary<string, List<FilterOption>>();

            var query = _unitOfWork.Context.RSS_ServiceTemplateView
                .Where(a => a.Deleted == false);

            foreach (var field in fieldsList)
            {
                var fieldOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

                switch (field.field)
                {
                    case "serviceCategoryTitle":
                        var categoryOptions = query
                            .Where(x => !string.IsNullOrEmpty(x.ServiceCategoryTitle))
                            .GroupBy(x => x.ServiceCategoryTitle)
                            .Select(g => new FilterOption { name = g.Key, value = g.Key })
                            .OrderBy(x => x.name)
                            .ToList();
                        fieldOptions.AddRange(categoryOptions);
                        break;

                    case "manufacturerDescription":
                        var manufacturerOptions = query
                            .Where(x => !string.IsNullOrEmpty(x.ManufacturerDescription))
                            .GroupBy(x => x.ManufacturerDescription)
                            .Select(g => new FilterOption { name = g.Key, value = g.Key })
                            .OrderBy(x => x.name)
                            .ToList();
                        fieldOptions.AddRange(manufacturerOptions);
                        break;
                }

                filterOptions[field.field] = fieldOptions;
            }

            return filterOptions;
        }

        public Dictionary<string, Dictionary<string, int>> GetFilterCountData(FilterDataDto filterData)
        {
            _unitOfWork.ReadOnly();

            var filterCountData = new Dictionary<string, Dictionary<string, int>>();

            var baseQuery = _unitOfWork.Context.RSS_ServiceTemplateView
                .Where(a => a.Deleted == false);

            // Apply search filters if any
            if (filterData.paging?.Filter != null && filterData.paging.Filter.Any())
            {
                baseQuery = ApplySearchFilters(baseQuery, filterData.paging.Filter);
            }

            foreach (var field in filterData.fields)
            {
                var fieldCounts = new Dictionary<string, int>();

                // For each field, calculate counts considering all OTHER applied filters
                var queryForField = baseQuery;

                // Apply all filters EXCEPT the current field
                var otherFilters = filterData.appliedFilters.Where(kvp => kvp.Key != field.field).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                if (otherFilters.Any())
                {
                    queryForField = ApplyMultiFilters(queryForField, filterData.fields.Where(f => f.field != field.field).ToList(), otherFilters);
                }

                switch (field.field)
                {
                    case "serviceCategoryTitle":
                        var categoryCounts = queryForField
                            .Where(x => !string.IsNullOrEmpty(x.ServiceCategoryTitle))
                            .GroupBy(x => x.ServiceCategoryTitle)
                            .Select(g => new { Key = g.Key, Count = g.Count() })
                            .ToList();

                        foreach (var item in categoryCounts)
                        {
                            fieldCounts[item.Key] = item.Count;
                        }
                        break;

                    case "manufacturerDescription":
                        var manufacturerCounts = queryForField
                            .Where(x => !string.IsNullOrEmpty(x.ManufacturerDescription))
                            .GroupBy(x => x.ManufacturerDescription)
                            .Select(g => new { Key = g.Key, Count = g.Count() })
                            .ToList();

                        foreach (var item in manufacturerCounts)
                        {
                            fieldCounts[item.Key] = item.Count;
                        }
                        break;
                }

                filterCountData[field.field] = fieldCounts;
            }

            return filterCountData;
        }

        private IQueryable<RSS_ServiceTemplateView> ApplyMultiFilters(IQueryable<RSS_ServiceTemplateView> query, List<MultiFilterFieldDto> fields, Dictionary<string, object> appliedFilters)
        {
            foreach (var field in fields)
            {
                if (appliedFilters.ContainsKey(field.field))
                {
                    var filterValues = appliedFilters[field.field] as Newtonsoft.Json.Linq.JArray;
                    if (filterValues != null && filterValues.Count > 0)
                    {
                        var stringValues = filterValues.Select(v => v.ToString()).Where(v => v != "Any").ToList();
                        if (stringValues.Any())
                        {
                            switch (field.field)
                            {
                                case "serviceCategoryTitle":
                                    query = query.Where(x => stringValues.Contains(x.ServiceCategoryTitle));
                                    break;

                                case "manufacturerDescription":
                                    query = query.Where(x => stringValues.Contains(x.ManufacturerDescription));
                                    break;
                            }
                        }
                    }
                }
            }

            return query;
        }

        private IQueryable<RSS_ServiceTemplateView> ApplySearchFilters(IQueryable<RSS_ServiceTemplateView> query, List<FilterDescriptor> searchFilters)
        {
            foreach (var filter in searchFilters)
            {
                switch (filter.Field.ToLower())
                {
                    case "description":
                        if (filter.Operator == FilterOperator.Contains || filter.Operator == FilterOperator.StartsWith)
                        {
                            query = query.Where(x => x.Description.Contains(filter.Value.ToString()));
                        }
                        break;

                    case "servicecategorytitle":
                        if (filter.Operator == FilterOperator.Contains)
                        {
                            query = query.Where(x => x.ServiceCategoryTitle.Contains(filter.Value.ToString()));
                        }
                        break;

                    case "manufacturerdescription":
                        if (filter.Operator == FilterOperator.Contains)
                        {
                            query = query.Where(x => x.ManufacturerDescription.Contains(filter.Value.ToString()));
                        }
                        break;
                }
            }

            return query;
        }
    }
}
