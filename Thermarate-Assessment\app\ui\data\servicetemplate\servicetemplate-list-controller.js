(function () {
    // The ConstructionListCtrl supports a list page.
    'use strict';
    var controllerId = 'ServiceTemplateListCtrl';
    angular.module('app')
    .controller(controllerId, ['$rootScope', '$scope', '$mdDialog', 'common', 'servicetemplateservice', 'manufacturerservice', 'daterangehelper', '$state', serviceTemplateListController]);
    function serviceTemplateListController($rootScope, $scope, $mdDialog, common, servicetemplateservice, manufacturerservice, daterangehelper, $state) {
        // The model for this form
        var vm = this;
        var modalInstance = {};
        vm.spinnerOptions = {};
        vm.isBusy = true;
        vm.title = 'Services Database';
        vm.serviceTemplateList = [];
        vm.listFilter = "";
        vm.actionButtons = [];
        vm.filterOptions = {};
        vm.currentFilter = "All";
        vm.totalRecords = 0;
        vm.totalFilteredRecords = 0;
        vm.showingFromCnt = 0;
        vm.showingToCnt = 0;
        vm.currentQuery = {};

        // Multi-filter variables
        vm.searchString = null;
        vm.searchStringOld = null;
        vm.searchFields = ['description', 'serviceCategoryTitle', 'manufacturerDescription'];
        vm.filters = [
            { field: 'serviceCategoryTitle', name: 'category', section: 1 },
            { field: 'manufacturerDescription', name: 'manufacturer', section: 1 }
        ];
        vm.filteredFilters = [];
        vm.appliedFilters = {};
        vm.appliedFiltersOld = {};
        vm.filterCountData = {};
        vm.filtersApplied = false;
        vm.filtersExpanded = false;
        vm.initialiseComplete = false;
        vm.queryModel = {
            canSave: false,
            fields: [
                {
                    name: 'serviceCategoryCode',
                    description: 'Category',
                    dataType: 'string',
                    operators: []
                },
                {
                    name: 'description',
                    description: 'Description',
                    dataType: 'string',
                    operators: []
                },
            ],
        };

        const FILTER_NAME = "serviceTemplateListCtrl-customFilter";

        // Initialize filters
        vm.filteredFilters = angular.copy(vm.filters);
        vm.filters.forEach(filter => {
            vm.appliedFilters[filter.field] = ['Any'];
        });
        vm.appliedFiltersOld = angular.copy(vm.appliedFilters);

        // Multi-filter helper methods
        vm.keyToName = function(key) {
            switch(key) {
                case 'category': return 'Category';
                case 'manufacturer': return 'Manufacturer';
                default: return key;
            }
        };

        vm.anyOptionsSelectedOnField = function(filter, appliedFilters) {
            return appliedFilters[filter.field] && appliedFilters[filter.field].length > 0 &&
                   !appliedFilters[filter.field].includes('Any');
        };

        vm.anyFiltersApplied = function(searchString, filters, appliedFilters) {
            if (searchString && searchString.trim() !== '') return true;
            return filters.some(filter => vm.anyOptionsSelectedOnField(filter, appliedFilters));
        };

        vm.filteredAppliedFilters = function() {
            let filtered = {};
            Object.keys(vm.appliedFilters).forEach(key => {
                if (vm.appliedFilters[key] && vm.appliedFilters[key].length > 0 &&
                    !vm.appliedFilters[key].includes('Any')) {
                    filtered[key] = vm.appliedFilters[key];
                }
            });
            return filtered;
        };

        // Clear filter functions
        vm.clearFilter = function (filter) {
            vm.appliedFilters[filter.field] = ['Any'];
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters);
            vm.refreshList();
        };

        vm.clearFilters = function () {
            vm.appliedFilters = {};
            vm.appliedFiltersOld = {};
            vm.filterCountData = {};
            vm.searchString = null;
            vm.searchStringOld = null;
            vm.filters.forEach(filter => {
                vm.appliedFilters[filter.field] = ['Any'];
            });
            vm.filtersApplied = false;
            vm.refreshList();
        };

        vm.serviceTemplateCategories = [];
        servicetemplateservice.getServiceCategories()
            .then((data) => { vm.serviceTemplateCategories = data; });

        vm.manufacturers = [];
        manufacturerservice.getAllManufacturersList()
            .then((data) => { vm.manufacturers = data; });

        var persistRangeName = "serviceTemplateList-DtRange";
        vm.rptDateRange = daterangehelper.getDefaultRange('All Time', persistRangeName);
        vm.ranges = daterangehelper.getRanges('Today', 'Yesterday', 'This Week', 'Last Week', 'This Month', 'Last Month',
                                                'This Quarter', 'Last Quarter', 'Current Year', 'Current Financial Year', 'Last Financial Year',
                                                'Last Year', '12 Months', 'All Time');

        //Repopulate the List after Refresh Page
        vm.refreshList = function (filter) {
            if (!vm.initialiseComplete) {
                vm.callServer(null, null, filter);
                localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
                return;
            }

            vm.isBusy = true;
            vm.filtersApplied = vm.anyFiltersApplied(vm.searchString, vm.filters, vm.appliedFilters);

            // Create search filter
            let searchFilter = [];
            if (vm.searchString && vm.searchString.trim() !== '') {
                for (let i = 0; i < vm.searchFields.length; i++) {
                    if (i == vm.searchFields.length-1) {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString });
                    } else {
                        searchFilter.push({ field: vm.searchFields[i], operator: "contains", value: vm.searchString, logic: "or" });
                    }
                }
            }

            // Get filtered data
            servicetemplateservice.getListMultiFiltered(100, 1, null, vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(
                result => {
                    if (result == undefined || result == null)
                        return; // Its been cancelled so get out of here.

                    vm.serviceTemplateList = result.data;
                    vm.totalFilteredRecords = result.total;
                    vm.showingFromCnt = vm.serviceTemplateList.length > 0 ? 1 : 0;
                    vm.showingToCnt = vm.serviceTemplateList.length;

                    // Sort favorited items to the top
                    if (vm.serviceTemplateList && vm.serviceTemplateList.length > 0) {
                        vm.serviceTemplateList.sort((a, b) => {
                            if (a.isFavourite && !b.isFavourite) return -1;
                            if (!a.isFavourite && b.isFavourite) return 1;
                            return 0;
                        });
                    }

                    // Get filter count data
                    servicetemplateservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), searchFilter).then(data => {
                        vm.filterCountData = data;
                        vm.isBusy = false;
                    });
                },
                error => vm.isBusy = false
            );
            localStorage.setItem(persistRangeName, JSON.stringify(vm.rptDateRange));
        };

        vm.createServiceTemplate = function () {
            var modalScope = $rootScope.$new();
            modalScope.viewMode = "New";
            modalScope.newRecord = true;
            var modalOptions = {
                templateUrl: 'app/ui/data/servicetemplate/servicetemplate-update.html',
                scope: modalScope,
                resolve: {
                    viewMode: function () {
                        return 'New';
                    }
                }
            };
            modalScope.modalInstance = $mdDialog.show(modalOptions);
            modalScope.modalInstance.then(function (data) {
                // Returned from modal, so refresh list.
                vm.refreshList(null);
            }, function () {
                vm.refreshList(null);
                // Cancelled.
            })['finally'](function () {
                modalScope.modalInstance = undefined  // <--- This fixes
            });
        }

        var saveTableState = null;
        vm.callServer = function callServer(tableState, e, filter) {
            if (tableState != null) {
                saveTableState = tableState;
            }
            if (saveTableState == null || vm.currentQuery == null || vm.currentQuery.queryName == null) {
                return;
            }

            var pagination = saveTableState.pagination;

            var start = pagination.start || 0;     // This is NOT the page number, but the index of item in the list that you want to use to display the table.
            var pageSize = pagination.number || 100;  // Number of entries showed per page.
            var pageIndex = (start / pageSize) + 1;

            vm.isBusy = true;
            var sort = {};
            if (saveTableState.sort != null) {
                sort.field = saveTableState.sort.predicate;
                sort.dir = saveTableState.sort.reverse ? "desc" : "asc";
            }

            if (filter == null) {
                if (saveTableState.search != null && saveTableState.search.predicateObject != null && saveTableState.search.predicateObject.$ != null) {
                    var val = saveTableState.search.predicateObject.$;
                    // Adjust here for the columns quick search will search.
                    filter = [{ field: "description", operator: "startswith", value: val, logic: "or" },
                        { field: "serviceCategoryTitle", operator: "contains", value: val }];
                }
                if (vm.currentQuery != null && vm.currentQuery.filter != null && vm.currentQuery.filter.length > 0) {
                    filter = vm.currentQuery.filter;
                }
            }

            daterangehelper.correctRangeDates(vm.rptDateRange);
            servicetemplateservice.getListCancel();
            servicetemplateservice.getList(vm.listFilter, vm.rptDateRange.startDate.toISOString(), vm.rptDateRange.endDate.toISOString(), pageSize, pageIndex, sort, filter)
                .then(function (result) {
                    if (result == undefined || result == null) {
                        // Its been cancelled so get out of here.
                        return;
                    }
                    vm.currentFilter = servicetemplateservice.currentFilter();
                    vm.serviceTemplateList = result.data;

                    // Sort favorited items to the top
                    if (vm.serviceTemplateList && vm.serviceTemplateList.length > 0) {
                        vm.serviceTemplateList.sort((a, b) => {
                            if (a.isFavourite && !b.isFavourite) return -1;
                            if (!a.isFavourite && b.isFavourite) return 1;
                            return 0;
                        });
                    }

                    vm.totalRecords = result.total;
                    saveTableState.pagination.numberOfPages = Math.ceil(result.total / pageSize); //set the number of pages so the pagination can update
                    vm.showingFromCnt = vm.serviceTemplateList.length > 0 ? start + 1 : 0;
                    vm.showingToCnt = start + result.data.length;
                    vm.isBusy = false;
                },
                function (error) {
                    vm.isBusy = false;
                });
        };

        vm.delete = async function (row, showSuccessMessage = true) {

            let index = vm.serviceTemplateList.indexOf(row);

            await servicetemplateservice.deleteServiceTemplate(row.serviceTemplateId, showSuccessMessage);
            row.deleted = true;

            vm.serviceTemplateList.splice(index, 1);

        }

        vm.clone = function (row) {

            servicetemplateservice
                .copyServiceTemplate(row.serviceTemplateId, true) // Show success message for individual clones
                .then((id) => {

                    servicetemplateservice
                        .getServiceTemplate(id)
                        .then(template => {

                            // Transform the returned construction
                            // so it matches our view data.
                            let transform = {
                                ...template,
                                serviceCategoryTitle: template.serviceCategory.title
                            };

                            // Add returned template to list.
                            vm.serviceTemplateList.push(transform);

                            // Ideally sort based on what we're actually sorting by, but since
                            // we basically only have the Template Name to go off...
                            vm.serviceTemplateList.sort((a, b) => (a.description > b.description)
                                ? 1
                                : ((b.templateName > a.templateName)
                                    ? 1
                                    : -1));
                        });

                });
        }

        vm.bulkSelect = function (state) {
            vm.serviceTemplateList.forEach(x => x.isBulkSelected = state);
        }

        vm.bulkSelectionsExist = function () {
            return vm.serviceTemplateList.some(x => x.isBulkSelected);
        }

        vm.bulkDelete = async function() {
            let toDelete = vm.serviceTemplateList.filter(x => x.isBulkSelected);
            const deletePromises = [];

            for (let i = 0; i < toDelete.length; i++) {
                deletePromises.push(servicetemplateservice.deleteServiceTemplate(toDelete[i].serviceTemplateId, false));
            }

            await Promise.all(deletePromises);

            toDelete.forEach(item => {
                item.deleted = true;
                let index = vm.serviceTemplateList.indexOf(item);
                if (index !== -1) {
                    vm.serviceTemplateList.splice(index, 1);
                }
            });

            vm.serviceTemplateList.forEach(item => item.isBulkSelected = false);
            vm.bulkSelected = false;

            common.logger.logSuccess(`${toDelete.length} Service Templates deleted successfully`);
        }

        vm.addCustomFilter = function () {

            let categoriesTransformed = vm.serviceTemplateCategories.map(x => ({ title: x.title, value: x.serviceCategoryCode }));
            // Just match on description otherwise the search filter will display the id...
            let manufacturersTransformed = vm.manufacturers.map(x => ({ title: x.description, value: x.description }));

            let options = [
                {
                    title: "Service Category",
                    field: 'serviceCategoryCode',
                    operator: 'eq',
                    elementType: "select",
                    values: categoriesTransformed
                },
                {
                    title: "Manufacturer",
                    field: 'manufacturerDescription',
                    operator: 'eq',
                    elementType: "select",
                    values: manufacturersTransformed
                }
            ];

            var modalScope = $rootScope.$new(true);
            modalScope.options = options;

            $mdDialog.show({
                templateUrl: 'app/ui/data/generic-filter-modal.html',
                parent: angular.element(document.body),
                clickOutsideToClose: true,
                scope: modalScope,
                skipHide: true, // DON'T HIDE THE MODAL
            })
                .then(function (response) {
                    vm.customFilter = response;
                    vm.refreshList(vm.customFilter);
                    localStorage.setItem(FILTER_NAME, JSON.stringify(vm.customFilter));

                }, function () { });
        };

        vm.removeCustomFilter = function (f) {

            vm.customFilter = vm.customFilter.filter(x => x.field != f.field);

            if (vm.customFilter.length == 0) {
                vm.customFilter = null;
            }

            localStorage.setItem(FILTER_NAME, JSON.stringify(vm.customFilter));
            vm.refreshList(vm.customFilter);
        };

        vm.getPreviousCustomFilters = function () {
            var savedState = JSON.parse(localStorage.getItem(FILTER_NAME));

            if (savedState) {
                vm.customFilter = savedState;
                vm.refreshList(vm.customFilter);
            }
        }

        setTimeout(() => vm.getPreviousCustomFilters(), 200);

        // Initialize multi-filters
        vm.initializeMultiFilters = function() {
            vm.isBusy = true;

            // Use Promise.all to make concurrent API calls
            Promise.all([
                servicetemplateservice.getList(null, null, null, 100, 1, null, null),
                servicetemplateservice.getMultiFilterOptions(vm.filteredFilters)
            ]).then(results => {
                // Process service templates data (first promise result)
                const serviceTemplatesResult = results[0];
                vm.serviceTemplateList = serviceTemplatesResult.data;
                vm.totalRecords = serviceTemplatesResult.total;
                vm.totalFilteredRecords = vm.totalRecords;

                // Sort favorited items to the top
                if (vm.serviceTemplateList && vm.serviceTemplateList.length > 0) {
                    vm.serviceTemplateList.sort((a, b) => {
                        if (a.isFavourite && !b.isFavourite) return -1;
                        if (!a.isFavourite && b.isFavourite) return 1;
                        return 0;
                    });
                }

                vm.showingFromCnt = vm.serviceTemplateList.length > 0 ? 1 : 0;
                vm.showingToCnt = vm.serviceTemplateList.length;

                // Process filter options (second promise result)
                const filterOptions = results[1];
                vm.filterOptions = filterOptions;

                // Mark initialization as complete
                vm.initialiseComplete = true;
                vm.isBusy = false;

                // Get initial filter count data
                servicetemplateservice.getFilterCountData(vm.filteredFilters, vm.filterOptions, vm.filteredAppliedFilters(), []).then(data => {
                    vm.filterCountData = data;
                });
            }).catch(error => {
                console.error('Error initializing multi-filters:', error);
                vm.isBusy = false;
            });
        };

        // Initialize on load
        vm.initializeMultiFilters();

        vm.createCustomFilterLabel = function(field) {
            let overriddenField = field;

            // Override for UI
            if (field === 'manufacturerDescription')
                overriddenField = 'Manufacturer';
            else if (field === 'serviceCategoryCode')
                overriddenField = 'Service Category';
            else
                overriddenField = vm.toSplitTitleCase(overriddenField);

            return overriddenField;
        }

        vm.toSplitTitleCase = (s) => common.toSplitTitleCase(s);

        function setActionButtons() {
            vm.actionButtons = [];
            vm.actionButtons.push({
                onclick: vm.createServiceTemplate,
                name: 'Add New',
                desc: 'Add New',
                roles: ['settings__settings__create'],
            });
        }

        setActionButtons();

        /**
         * Attempts to process the given excel files. Will show a dialog with
         * further actions required depending on how the processing went
         * (i.e. if any warnings or errors were encountered)
         *
         * @param {any} file Excel file to upload
         */
        vm.uploadFile = function (file) {

            if (file == null)
                return;

            // TODO: Get this working! Will need to have a new template to
            // replace the construction-import below I *THINK*. Maybe if we get
            // the data format the same it will 'just work' (or we can modify it
            // to make it more generic).
            servicetemplateservice.uploadTemplateDatabase(file, false)
                .then((data) => {
                    var modalScope = $rootScope.$new();
                    modalScope.excelData = file;
                    modalScope.response = data;
                    modalScope.isInitialProcess = true;
                    modalScope.extractor = 'services';

                    modalScope.modalInstance = $mdDialog.show({
                        templateUrl: 'app/ui/data/construction/construction-import.html',
                        scope: modalScope,
                    });
                });
        }

        /**
         * Exports the database to an Excel file in the same format as expected for imports
         */
        vm.exportDatabase = function () {
            vm.isBusy = true;
            servicetemplateservice.exportTemplateDatabase()
                .then(() => {
                    vm.isBusy = false;
                })
                .catch(() => {
                    vm.isBusy = false;
                });
        }

        vm.goToServiceTemplate = function(serviceTemplateId) {
            $state.go("servicetemplate-updateform", { serviceTemplateId: serviceTemplateId });
        }

        vm.setFavouriteStatus = function(serviceTemplateId, isFavourite) {
            servicetemplateservice.setIsFavourite(serviceTemplateId, isFavourite)
                .then(function() {
                    // Update the local list item
                    const item = vm.serviceTemplateList.find(x => x.serviceTemplateId === serviceTemplateId);
                    if (item) {
                        item.isFavourite = isFavourite;
                    }
                });
        }

        vm.showBulkEditModal = function() {
            let selectedItems = vm.serviceTemplateList.filter(x => x.isBulkSelected);

            if (selectedItems.length === 0) {
                return;
            }

            var modalScope = $rootScope.$new();
            modalScope.title = "Bulk Edit";

            $mdDialog.show({
                templateUrl: 'app/ui/data/servicetemplate/bulk-edit-servicetemplate-modal.html',
                scope: modalScope,
                clickOutsideToClose: false
            }).then(function(action) {
                if (action === 'DELETE') {
                    vm.bulkDelete();
                } else if (action === 'COPY') {
                    vm.bulkCopy(selectedItems);
                } else if (action === 'EXPORT') {
                    vm.bulkExport();
                }
            });
        }

        vm.bulkCopy = function(items) {
            const promises = [];

            // Clone each item without showing individual success messages
            items.forEach(item => {
                promises.push(servicetemplateservice
                    .copyServiceTemplate(item.serviceTemplateId, false)
                    .then((id) => {
                        return servicetemplateservice
                            .getServiceTemplate(id)
                            .then(template => {
                                // Transform the returned construction
                                // so it matches our view data.
                                let transform = {
                                    ...template,
                                    serviceCategoryTitle: template.serviceCategory.title
                                };

                                // Add returned template to list.
                                vm.serviceTemplateList.push(transform);
                            });
                    }));
            });

            // Show a single success message after all items are copied
            Promise.all(promises).then(() => {
                // Sort the list
                vm.serviceTemplateList.sort((a, b) => (a.description > b.description)
                    ? 1
                    : ((b.templateName > a.templateName)
                        ? 1
                        : -1));

                // Clear all selections
                items.forEach(item => item.isBulkSelected = false);
                vm.bulkSelected = false;

                // Show a single success message
                common.logger.logSuccess(`${items.length} Service Templates copied successfully`);
            });
        }

        vm.bulkExport = function () {
            vm.isBusy = true;
            let selectedItems = vm.serviceTemplateList.filter(x => x.isBulkSelected);
            let selectedIds = selectedItems.map(item => item.serviceTemplateId);

            servicetemplateservice.exportTemplateDatabase(selectedIds)
                .then(() => {
                    vm.isBusy = false;

                    // Clear all selections
                    selectedItems.forEach(item => item.isBulkSelected = false);
                    vm.bulkSelected = false;

                    // Show a success message
                    common.logger.logSuccess(`${selectedItems.length} Service Templates exported successfully`);
                })
                .catch(() => {
                    vm.isBusy = false;
                });
        }
    }
})();